Component: ARM Compiler 5.06 update 3 (build 300) Tool: armlink [4d35c9]

==============================================================================

Section Cross References

    main.o(.text) refers to delay.o(.text) for Delay_Init
    main.o(.text) refers to motor.o(.text) for Motor_Config
    main.o(.text) refers to sensor.o(.text) for SENSOR_GPIO_Config
    main.o(.text) refers to usart.o(.text) for Usart3_Init
    main.o(.text) refers to iic.o(.text) for IIC_Init
    main.o(.text) refers to line.o(.text) for track_zhixian1
    system_stm32f10x.o(.text) refers to system_stm32f10x.o(.data) for SystemCoreClock
    startup_stm32f10x_hd.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to delay.o(.text) for SysTick_Handler
    startup_stm32f10x_hd.o(RESET) refers to usart.o(.text) for USART3_IRQHandler
    startup_stm32f10x_hd.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(.text) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(HEAP) for Heap_Mem
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(STACK) for Stack_Mem
    stm32f10x_adc.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_BackupResetCmd
    stm32f10x_can.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f10x_cec.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f10x_gpio.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f10x_pwr.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(.text) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_spi.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_wwdg.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB1PeriphResetCmd
    line.o(.text) refers to sensor.o(.text) for digtal
    line.o(.text) refers to delay.o(.text) for Delay_ms
    line.o(.text) refers to motor.o(.text) for motor
    line.o(.text) refers to line.o(.data) for lukou_num
    line.o(.text) refers to usart.o(.text) for Read_Data1
    line.o(.text) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    line.o(.text) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    line.o(.text) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    line.o(.text) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    line.o(.text) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    line.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    line.o(.text) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    line.o(.text) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    line.o(.text) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    motor.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB1PeriphClockCmd
    motor.o(.text) refers to stm32f10x_tim.o(.text) for TIM_TimeBaseInit
    motor.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    sensor.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    sensor.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    delay.o(.text) refers to system_stm32f10x.o(.data) for SystemCoreClock
    delay.o(.text) refers to delay.o(.data) for TimingDelay
    iic.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    iic.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    iic.o(.text) refers to delay.o(.text) for Delay_us
    iic.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB1PeriphClockCmd
    usart.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_PinRemapConfig
    usart.o(.text) refers to stm32f10x_usart.o(.text) for USART_Init
    usart.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    usart.o(.text) refers to delay.o(.text) for Delay_ms
    usart.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(.text) refers to usart.o(.data) for Num
    usart.o(.text) refers to usart.o(.bss) for USART_RX_STA
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffixr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixu) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixur) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_hd.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing stm32f10x_adc.o(.text), (1102 bytes).
    Removing stm32f10x_bkp.o(.text), (196 bytes).
    Removing stm32f10x_can.o(.text), (2544 bytes).
    Removing stm32f10x_cec.o(.text), (288 bytes).
    Removing stm32f10x_crc.o(.text), (72 bytes).
    Removing stm32f10x_dac.o(.text), (396 bytes).
    Removing stm32f10x_dbgmcu.o(.text), (48 bytes).
    Removing stm32f10x_dma.o(.text), (596 bytes).
    Removing stm32f10x_exti.o(.text), (284 bytes).
    Removing stm32f10x_flash.o(.text), (1468 bytes).
    Removing stm32f10x_fsmc.o(.text), (1548 bytes).
    Removing stm32f10x_i2c.o(.text), (1028 bytes).
    Removing stm32f10x_iwdg.o(.text), (64 bytes).
    Removing stm32f10x_pwr.o(.text), (204 bytes).
    Removing stm32f10x_rtc.o(.text), (328 bytes).
    Removing stm32f10x_sdio.o(.text), (468 bytes).
    Removing stm32f10x_spi.o(.text), (780 bytes).
    Removing stm32f10x_wwdg.o(.text), (136 bytes).
    Removing sys.o(.text), (664 bytes).

20 unused section(s) (total 12246 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/ffix.s                          0x00000000   Number         0  ffix.o ABSOLUTE
    ../fplib/ffixu.s                         0x00000000   Number         0  ffixu.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ..\Core\core_cm3.c                       0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\Core\startup_stm32f10x_hd.s           0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\Core\system_stm32f10x.c               0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    ..\Hardware\LINE\line.c                  0x00000000   Number         0  line.o ABSOLUTE
    ..\Hardware\MOTOR\motor.c                0x00000000   Number         0  motor.o ABSOLUTE
    ..\Hardware\SENSOR\sensor.c              0x00000000   Number         0  sensor.o ABSOLUTE
    ..\Libraries\src\misc.c                  0x00000000   Number         0  misc.o ABSOLUTE
    ..\Libraries\src\stm32f10x_adc.c         0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    ..\Libraries\src\stm32f10x_bkp.c         0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    ..\Libraries\src\stm32f10x_can.c         0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    ..\Libraries\src\stm32f10x_cec.c         0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    ..\Libraries\src\stm32f10x_crc.c         0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    ..\Libraries\src\stm32f10x_dac.c         0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    ..\Libraries\src\stm32f10x_dbgmcu.c      0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    ..\Libraries\src\stm32f10x_dma.c         0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    ..\Libraries\src\stm32f10x_exti.c        0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    ..\Libraries\src\stm32f10x_flash.c       0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    ..\Libraries\src\stm32f10x_fsmc.c        0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    ..\Libraries\src\stm32f10x_gpio.c        0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\Libraries\src\stm32f10x_i2c.c         0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    ..\Libraries\src\stm32f10x_iwdg.c        0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    ..\Libraries\src\stm32f10x_pwr.c         0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    ..\Libraries\src\stm32f10x_rcc.c         0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\Libraries\src\stm32f10x_rtc.c         0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    ..\Libraries\src\stm32f10x_sdio.c        0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    ..\Libraries\src\stm32f10x_spi.c         0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    ..\Libraries\src\stm32f10x_tim.c         0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    ..\Libraries\src\stm32f10x_usart.c       0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\Libraries\src\stm32f10x_wwdg.c        0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    ..\System\DELAY\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\System\IIC\iic.c                      0x00000000   Number         0  iic.o ABSOLUTE
    ..\System\SYS\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\System\USART\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\User\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\User\stm32f10x_it.c                   0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    ..\\Core\\core_cm3.c                     0x00000000   Number         0  core_cm3.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0800016c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000188   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x080001a4   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001a6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001a6   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001a8   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x080001aa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x080001aa   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x080001ac   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001ac   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001ac   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001b2   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001b2   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001b6   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001b6   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001be   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001c0   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001c0   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001c4   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001ca   Section        0  main.o(.text)
    .text                                    0x080001fa   Section        0  stm32f10x_it.o(.text)
    .text                                    0x08000214   Section        0  system_stm32f10x.o(.text)
    SetSysClockTo72                          0x08000215   Thumb Code   214  system_stm32f10x.o(.text)
    SetSysClock                              0x080002eb   Thumb Code     8  system_stm32f10x.o(.text)
    .text                                    0x080003f4   Section       64  startup_stm32f10x_hd.o(.text)
    .text                                    0x08000434   Section        0  misc.o(.text)
    .text                                    0x08000510   Section        0  stm32f10x_gpio.o(.text)
    .text                                    0x0800086c   Section        0  stm32f10x_rcc.o(.text)
    .text                                    0x08000c10   Section        0  stm32f10x_tim.o(.text)
    TI4_Config                               0x080010b7   Thumb Code   130  stm32f10x_tim.o(.text)
    TI3_Config                               0x0800114b   Thumb Code   122  stm32f10x_tim.o(.text)
    TI2_Config                               0x080011df   Thumb Code   130  stm32f10x_tim.o(.text)
    TI1_Config                               0x08001273   Thumb Code   108  stm32f10x_tim.o(.text)
    .text                                    0x08001a2c   Section        0  stm32f10x_usart.o(.text)
    .text                                    0x08001e34   Section        0  line.o(.text)
    .text                                    0x08002884   Section        0  motor.o(.text)
    .text                                    0x08002a38   Section        0  sensor.o(.text)
    .text                                    0x08002b10   Section        0  delay.o(.text)
    TimeingDelay_Decrement                   0x08002bc9   Thumb Code    18  delay.o(.text)
    .text                                    0x08002bf8   Section        0  iic.o(.text)
    .text                                    0x08003028   Section        0  usart.o(.text)
    .text                                    0x080034c0   Section       78  rt_memclr_w.o(.text)
    .text                                    0x0800350e   Section        0  heapauxi.o(.text)
    .text                                    0x08003514   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x0800355e   Section        0  exit.o(.text)
    .text                                    0x08003570   Section        8  libspace.o(.text)
    .text                                    0x08003578   Section        0  sys_exit.o(.text)
    .text                                    0x08003584   Section        2  use_no_semi.o(.text)
    .text                                    0x08003586   Section        0  indicate_semi.o(.text)
    x$fpl$fadd                               0x08003588   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x08003597   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fdiv                               0x0800364c   Section      388  fdiv.o(x$fpl$fdiv)
    _fdiv1                                   0x0800364d   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$ffix                               0x080037d0   Section       54  ffix.o(x$fpl$ffix)
    x$fpl$ffixu                              0x08003808   Section       62  ffixu.o(x$fpl$ffixu)
    x$fpl$fflt                               0x08003848   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$ffltu                              0x08003878   Section       38  fflt_clz.o(x$fpl$ffltu)
    x$fpl$fmul                               0x080038a0   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x080039a2   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x08003a2e   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$fsub                               0x08003a38   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x08003a47   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    x$fpl$usenofp                            0x08003b22   Section        0  usenofp.o(x$fpl$usenofp)
    .data                                    0x20000000   Section       20  system_stm32f10x.o(.data)
    .data                                    0x20000014   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000014   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000024   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000028   Section       28  line.o(.data)
    L_Pwm                                    0x2000002c   Data           4  line.o(.data)
    R_Pwm                                    0x20000030   Data           4  line.o(.data)
    L_Pwm                                    0x20000034   Data           4  line.o(.data)
    R_Pwm                                    0x20000038   Data           4  line.o(.data)
    L_Pwm                                    0x2000003c   Data           4  line.o(.data)
    R_Pwm                                    0x20000040   Data           4  line.o(.data)
    .data                                    0x20000044   Section        4  delay.o(.data)
    .data                                    0x20000048   Section        1  usart.o(.data)
    .bss                                     0x2000004c   Section       22  usart.o(.bss)
    .bss                                     0x20000064   Section       96  libspace.o(.bss)
    HEAP                                     0x200000c8   Section      512  startup_stm32f10x_hd.o(HEAP)
    Heap_Mem                                 0x200000c8   Data         512  startup_stm32f10x_hd.o(HEAP)
    STACK                                    0x200002c8   Section     1024  startup_stm32f10x_hd.o(STACK)
    Stack_Mem                                0x200002c8   Data        1024  startup_stm32f10x_hd.o(STACK)
    __initial_sp                             0x200006c8   Data           0  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000147   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0800016d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000189   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x080001a5   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x080001a7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x080001a9   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x080001ab   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x080001ad   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001ad   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001ad   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001b3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001b3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001b7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001b7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001bf   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001c1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001c1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001c5   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    main                                     0x080001cb   Thumb Code    48  main.o(.text)
    NMI_Handler                              0x080001fb   Thumb Code     2  stm32f10x_it.o(.text)
    HardFault_Handler                        0x080001fd   Thumb Code     4  stm32f10x_it.o(.text)
    MemManage_Handler                        0x08000201   Thumb Code     4  stm32f10x_it.o(.text)
    BusFault_Handler                         0x08000205   Thumb Code     4  stm32f10x_it.o(.text)
    UsageFault_Handler                       0x08000209   Thumb Code     4  stm32f10x_it.o(.text)
    DebugMon_Handler                         0x0800020d   Thumb Code     2  stm32f10x_it.o(.text)
    SVC_Handler                              0x0800020f   Thumb Code     2  stm32f10x_it.o(.text)
    PendSV_Handler                           0x08000211   Thumb Code     2  stm32f10x_it.o(.text)
    SystemInit                               0x080002f3   Thumb Code    78  system_stm32f10x.o(.text)
    SystemCoreClockUpdate                    0x08000341   Thumb Code   142  system_stm32f10x.o(.text)
    Reset_Handler                            0x080003f5   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART1_IRQHandler                        0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x0800040f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __user_initial_stackheap                 0x08000411   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    NVIC_PriorityGroupConfig                 0x08000435   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x0800043f   Thumb Code   100  misc.o(.text)
    NVIC_SetVectorTable                      0x080004a3   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x080004b1   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x080004d3   Thumb Code    40  misc.o(.text)
    GPIO_DeInit                              0x08000511   Thumb Code   172  stm32f10x_gpio.o(.text)
    GPIO_AFIODeInit                          0x080005bd   Thumb Code    20  stm32f10x_gpio.o(.text)
    GPIO_Init                                0x080005d1   Thumb Code   278  stm32f10x_gpio.o(.text)
    GPIO_StructInit                          0x080006e7   Thumb Code    16  stm32f10x_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x080006f7   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadInputData                       0x08000709   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x08000711   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputData                      0x08000723   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_SetBits                             0x0800072b   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_ResetBits                           0x0800072f   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_WriteBit                            0x08000733   Thumb Code    10  stm32f10x_gpio.o(.text)
    GPIO_Write                               0x0800073d   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_PinLockConfig                       0x08000741   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_EventOutputConfig                   0x08000753   Thumb Code    26  stm32f10x_gpio.o(.text)
    GPIO_EventOutputCmd                      0x0800076d   Thumb Code     6  stm32f10x_gpio.o(.text)
    GPIO_PinRemapConfig                      0x08000773   Thumb Code   138  stm32f10x_gpio.o(.text)
    GPIO_EXTILineConfig                      0x080007fd   Thumb Code    66  stm32f10x_gpio.o(.text)
    GPIO_ETH_MediaInterfaceConfig            0x0800083f   Thumb Code     8  stm32f10x_gpio.o(.text)
    RCC_DeInit                               0x0800086d   Thumb Code    64  stm32f10x_rcc.o(.text)
    RCC_HSEConfig                            0x080008ad   Thumb Code    70  stm32f10x_rcc.o(.text)
    RCC_GetFlagStatus                        0x080008f3   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x0800092b   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x08000963   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_HSICmd                               0x08000977   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_PLLConfig                            0x0800097d   Thumb Code    24  stm32f10x_rcc.o(.text)
    RCC_PLLCmd                               0x08000995   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_SYSCLKConfig                         0x0800099b   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x080009ad   Thumb Code    10  stm32f10x_rcc.o(.text)
    RCC_HCLKConfig                           0x080009b7   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK1Config                          0x080009c9   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK2Config                          0x080009db   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ITConfig                             0x080009ef   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_USBCLKConfig                         0x08000a09   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ADCCLKConfig                         0x08000a11   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_LSEConfig                            0x08000a23   Thumb Code    50  stm32f10x_rcc.o(.text)
    RCC_LSICmd                               0x08000a55   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_RTCCLKConfig                         0x08000a5b   Thumb Code    12  stm32f10x_rcc.o(.text)
    RCC_RTCCLKCmd                            0x08000a67   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_GetClocksFreq                        0x08000a6f   Thumb Code   192  stm32f10x_rcc.o(.text)
    RCC_AHBPeriphClockCmd                    0x08000b2f   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x08000b49   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x08000b63   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x08000b7d   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x08000b97   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_BackupResetCmd                       0x08000bb1   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x08000bb9   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_MCOConfig                            0x08000bbf   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_ClearFlag                            0x08000bc5   Thumb Code    14  stm32f10x_rcc.o(.text)
    RCC_GetITStatus                          0x08000bd3   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ClearITPendingBit                    0x08000be7   Thumb Code     6  stm32f10x_rcc.o(.text)
    TIM_DeInit                               0x08000c11   Thumb Code   424  stm32f10x_tim.o(.text)
    TIM_TimeBaseInit                         0x08000db9   Thumb Code   122  stm32f10x_tim.o(.text)
    TIM_OC1Init                              0x08000e33   Thumb Code   132  stm32f10x_tim.o(.text)
    TIM_OC2Init                              0x08000eb7   Thumb Code   154  stm32f10x_tim.o(.text)
    TIM_OC3Init                              0x08000f51   Thumb Code   150  stm32f10x_tim.o(.text)
    TIM_OC4Init                              0x08000fe7   Thumb Code   182  stm32f10x_tim.o(.text)
    TIM_SetIC4Prescaler                      0x0800109d   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_SetIC3Prescaler                      0x08001139   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SetIC2Prescaler                      0x080011c5   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_SetIC1Prescaler                      0x08001261   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ICInit                               0x080012df   Thumb Code   150  stm32f10x_tim.o(.text)
    TIM_PWMIConfig                           0x08001375   Thumb Code   124  stm32f10x_tim.o(.text)
    TIM_BDTRConfig                           0x080013f1   Thumb Code    32  stm32f10x_tim.o(.text)
    TIM_TimeBaseStructInit                   0x08001411   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OCStructInit                         0x08001423   Thumb Code    20  stm32f10x_tim.o(.text)
    TIM_ICStructInit                         0x08001437   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_BDTRStructInit                       0x08001449   Thumb Code    40  stm32f10x_tim.o(.text)
    TIM_Cmd                                  0x08001471   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_CtrlPWMOutputs                       0x08001489   Thumb Code    30  stm32f10x_tim.o(.text)
    TIM_ITConfig                             0x080014a7   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_GenerateEvent                        0x080014b9   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_DMAConfig                            0x080014bd   Thumb Code    10  stm32f10x_tim.o(.text)
    TIM_DMACmd                               0x080014c7   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_InternalClockConfig                  0x080014d9   Thumb Code    12  stm32f10x_tim.o(.text)
    TIM_SelectInputTrigger                   0x080014e5   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ITRxExternalClockConfig              0x080014f7   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_TIxExternalClockConfig               0x0800150f   Thumb Code    62  stm32f10x_tim.o(.text)
    TIM_ETRConfig                            0x0800154d   Thumb Code    28  stm32f10x_tim.o(.text)
    TIM_ETRClockMode1Config                  0x08001569   Thumb Code    54  stm32f10x_tim.o(.text)
    TIM_ETRClockMode2Config                  0x0800159f   Thumb Code    32  stm32f10x_tim.o(.text)
    TIM_PrescalerConfig                      0x080015bf   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_CounterModeConfig                    0x080015c5   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_EncoderInterfaceConfig               0x080015d7   Thumb Code    66  stm32f10x_tim.o(.text)
    TIM_ForcedOC1Config                      0x08001619   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ForcedOC2Config                      0x0800162b   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_ForcedOC3Config                      0x08001645   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ForcedOC4Config                      0x08001657   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_ARRPreloadConfig                     0x08001671   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_SelectCOM                            0x08001689   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_SelectCCDMA                          0x080016a1   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_CCPreloadControl                     0x080016b9   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_OC1PreloadConfig                     0x080016d1   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC2PreloadConfig                     0x080016e3   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC3PreloadConfig                     0x080016fd   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC4PreloadConfig                     0x0800170f   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC1FastConfig                        0x08001729   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC2FastConfig                        0x0800173b   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC3FastConfig                        0x08001755   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC4FastConfig                        0x08001767   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_ClearOC1Ref                          0x08001781   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ClearOC2Ref                          0x08001793   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_ClearOC3Ref                          0x080017ab   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ClearOC4Ref                          0x080017bd   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_OC1PolarityConfig                    0x080017d5   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC1NPolarityConfig                   0x080017e7   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_OC2PolarityConfig                    0x080017f9   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC2NPolarityConfig                   0x08001813   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC3PolarityConfig                    0x0800182d   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC3NPolarityConfig                   0x08001847   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_OC4PolarityConfig                    0x08001861   Thumb Code    26  stm32f10x_tim.o(.text)
    TIM_CCxCmd                               0x0800187b   Thumb Code    30  stm32f10x_tim.o(.text)
    TIM_CCxNCmd                              0x08001899   Thumb Code    30  stm32f10x_tim.o(.text)
    TIM_SelectOCxM                           0x080018b7   Thumb Code    82  stm32f10x_tim.o(.text)
    TIM_UpdateDisableConfig                  0x08001909   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_UpdateRequestConfig                  0x08001921   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_SelectHallSensor                     0x08001939   Thumb Code    24  stm32f10x_tim.o(.text)
    TIM_SelectOnePulseMode                   0x08001951   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SelectOutputTrigger                  0x08001963   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SelectSlaveMode                      0x08001975   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SelectMasterSlaveMode                0x08001987   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_SetCounter                           0x08001999   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetAutoreload                        0x0800199d   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetCompare1                          0x080019a1   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetCompare2                          0x080019a5   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetCompare3                          0x080019a9   Thumb Code     4  stm32f10x_tim.o(.text)
    TIM_SetCompare4                          0x080019ad   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_SetClockDivision                     0x080019b3   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_GetCapture1                          0x080019c5   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetCapture2                          0x080019cb   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetCapture3                          0x080019d1   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetCapture4                          0x080019d7   Thumb Code     8  stm32f10x_tim.o(.text)
    TIM_GetCounter                           0x080019df   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetPrescaler                         0x080019e5   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetFlagStatus                        0x080019eb   Thumb Code    18  stm32f10x_tim.o(.text)
    TIM_ClearFlag                            0x080019fd   Thumb Code     6  stm32f10x_tim.o(.text)
    TIM_GetITStatus                          0x08001a03   Thumb Code    34  stm32f10x_tim.o(.text)
    TIM_ClearITPendingBit                    0x08001a25   Thumb Code     6  stm32f10x_tim.o(.text)
    USART_DeInit                             0x08001a2d   Thumb Code   134  stm32f10x_usart.o(.text)
    USART_Init                               0x08001ab3   Thumb Code   210  stm32f10x_usart.o(.text)
    USART_StructInit                         0x08001b85   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_ClockInit                          0x08001b9d   Thumb Code    34  stm32f10x_usart.o(.text)
    USART_ClockStructInit                    0x08001bbf   Thumb Code    12  stm32f10x_usart.o(.text)
    USART_Cmd                                0x08001bcb   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_ITConfig                           0x08001be3   Thumb Code    74  stm32f10x_usart.o(.text)
    USART_DMACmd                             0x08001c2d   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_SetAddress                         0x08001c3f   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_WakeUpConfig                       0x08001c51   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x08001c63   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x08001c7b   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_LINCmd                             0x08001c8d   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_SendData                           0x08001ca5   Thumb Code     8  stm32f10x_usart.o(.text)
    USART_ReceiveData                        0x08001cad   Thumb Code    10  stm32f10x_usart.o(.text)
    USART_SendBreak                          0x08001cb7   Thumb Code    10  stm32f10x_usart.o(.text)
    USART_SetGuardTime                       0x08001cc1   Thumb Code    16  stm32f10x_usart.o(.text)
    USART_SetPrescaler                       0x08001cd1   Thumb Code    16  stm32f10x_usart.o(.text)
    USART_SmartCardCmd                       0x08001ce1   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_SmartCardNACKCmd                   0x08001cf9   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_HalfDuplexCmd                      0x08001d11   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_OverSampling8Cmd                   0x08001d29   Thumb Code    22  stm32f10x_usart.o(.text)
    USART_OneBitMethodCmd                    0x08001d3f   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_IrDAConfig                         0x08001d57   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_IrDACmd                            0x08001d69   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_GetFlagStatus                      0x08001d81   Thumb Code    26  stm32f10x_usart.o(.text)
    USART_ClearFlag                          0x08001d9b   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_GetITStatus                        0x08001dad   Thumb Code    84  stm32f10x_usart.o(.text)
    USART_ClearITPendingBit                  0x08001e01   Thumb Code    52  stm32f10x_usart.o(.text)
    track_zhixian1                           0x08001e35   Thumb Code  1364  line.o(.text)
    track_zhixian2                           0x08002389   Thumb Code   344  line.o(.text)
    track_PID1                               0x080024e1   Thumb Code   298  line.o(.text)
    track_PID2                               0x0800260b   Thumb Code   288  line.o(.text)
    track_PID3                               0x0800272b   Thumb Code   338  line.o(.text)
    Motor_Config                             0x08002885   Thumb Code   188  motor.o(.text)
    motor                                    0x08002941   Thumb Code   228  motor.o(.text)
    SENSOR_GPIO_Config                       0x08002a39   Thumb Code    40  sensor.o(.text)
    digtal                                   0x08002a61   Thumb Code   168  sensor.o(.text)
    Delay_Init                               0x08002b11   Thumb Code   106  delay.o(.text)
    Delay_us                                 0x08002b7b   Thumb Code    36  delay.o(.text)
    Delay_ms                                 0x08002b9f   Thumb Code    42  delay.o(.text)
    SysTick_Handler                          0x08002bdb   Thumb Code     8  delay.o(.text)
    IIC_Init                                 0x08002bf9   Thumb Code    46  iic.o(.text)
    IIC_Start                                0x08002c27   Thumb Code    64  iic.o(.text)
    IIC_Stop                                 0x08002c67   Thumb Code    66  iic.o(.text)
    IIC_Wait_Ack                             0x08002ca9   Thumb Code    90  iic.o(.text)
    IIC_Ack                                  0x08002d03   Thumb Code    70  iic.o(.text)
    IIC_NAck                                 0x08002d49   Thumb Code    70  iic.o(.text)
    IIC_Write_Byte                           0x08002d8f   Thumb Code    98  iic.o(.text)
    IIC_Read_Byte                            0x08002df1   Thumb Code   102  iic.o(.text)
    Read_IICData1                            0x08002e57   Thumb Code    58  iic.o(.text)
    Read_IICData2                            0x08002e91   Thumb Code   122  iic.o(.text)
    Read_IICData3                            0x08002f0b   Thumb Code   250  iic.o(.text)
    Set_ID                                   0x08003005   Thumb Code    36  iic.o(.text)
    Usart3_Init                              0x08003029   Thumb Code   184  usart.o(.text)
    Read_Data1                               0x080030e1   Thumb Code   142  usart.o(.text)
    Read_Data2                               0x0800316f   Thumb Code   160  usart.o(.text)
    Read_Data3                               0x0800320f   Thumb Code   222  usart.o(.text)
    Read_Data4                               0x080032ed   Thumb Code   228  usart.o(.text)
    Set_Data1                                0x080033d1   Thumb Code   176  usart.o(.text)
    USART3_IRQHandler                        0x08003481   Thumb Code    52  usart.o(.text)
    __aeabi_memclr4                          0x080034c1   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080034c1   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080034c1   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080034c5   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x0800350f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08003511   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08003513   Thumb Code     2  heapauxi.o(.text)
    __user_setup_stackheap                   0x08003515   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x0800355f   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x08003571   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08003571   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08003571   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x08003579   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08003585   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08003585   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08003587   Thumb Code     0  indicate_semi.o(.text)
    __aeabi_fadd                             0x08003589   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x08003589   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __aeabi_fdiv                             0x0800364d   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x0800364d   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_f2iz                             0x080037d1   Thumb Code     0  ffix.o(x$fpl$ffix)
    _ffix                                    0x080037d1   Thumb Code    54  ffix.o(x$fpl$ffix)
    __aeabi_f2uiz                            0x08003809   Thumb Code     0  ffixu.o(x$fpl$ffixu)
    _ffixu                                   0x08003809   Thumb Code    62  ffixu.o(x$fpl$ffixu)
    __aeabi_i2f                              0x08003849   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x08003849   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __aeabi_ui2f                             0x08003879   Thumb Code     0  fflt_clz.o(x$fpl$ffltu)
    _ffltu                                   0x08003879   Thumb Code    38  fflt_clz.o(x$fpl$ffltu)
    __aeabi_fmul                             0x080038a1   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x080038a1   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x080039a3   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x08003a2f   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_fsub                             0x08003a39   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x08003a39   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    __I$use$fp                               0x08003b22   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x08003b24   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08003b44   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x20000004   Data          16  system_stm32f10x.o(.data)
    lukou_num                                0x20000028   Data           1  line.o(.data)
    TimingDelay                              0x20000044   Data           4  delay.o(.data)
    Num                                      0x20000048   Data           1  usart.o(.data)
    USART_RX_STA                             0x2000004c   Data          22  usart.o(.bss)
    __libspace_start                         0x20000064   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200000c4   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00003b90, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Base: 0x08000000, Size: 0x00003b44, Max: 0x00080000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x00000130   Data   RO          168    RESET               startup_stm32f10x_hd.o
    0x08000130   0x00000008   Code   RO          555  * !!!main             c_w.l(__main.o)
    0x08000138   0x00000034   Code   RO          743    !!!scatter          c_w.l(__scatter.o)
    0x0800016c   0x0000001a   Code   RO          745    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000186   0x00000002   PAD
    0x08000188   0x0000001c   Code   RO          747    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a4   0x00000002   Code   RO          613    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001a6   0x00000000   Code   RO          620    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO          622    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO          625    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO          627    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO          629    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO          632    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO          634    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO          636    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO          638    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO          640    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO          642    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO          644    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO          646    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO          648    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO          650    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO          652    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO          656    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO          658    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO          660    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001a6   0x00000000   Code   RO          662    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001a6   0x00000002   Code   RO          663    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001a8   0x00000002   Code   RO          683    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001aa   0x00000000   Code   RO          696    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080001aa   0x00000000   Code   RO          698    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080001aa   0x00000000   Code   RO          701    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x080001aa   0x00000000   Code   RO          704    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x080001aa   0x00000000   Code   RO          706    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080001aa   0x00000000   Code   RO          709    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080001aa   0x00000002   Code   RO          710    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x080001ac   0x00000000   Code   RO          583    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001ac   0x00000000   Code   RO          590    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001ac   0x00000006   Code   RO          602    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001b2   0x00000000   Code   RO          592    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001b2   0x00000004   Code   RO          593    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001b6   0x00000000   Code   RO          595    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001b6   0x00000008   Code   RO          596    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001be   0x00000002   Code   RO          617    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001c0   0x00000000   Code   RO          665    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001c0   0x00000004   Code   RO          666    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001c4   0x00000006   Code   RO          667    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001ca   0x00000030   Code   RO            1    .text               main.o
    0x080001fa   0x00000018   Code   RO          122    .text               stm32f10x_it.o
    0x08000212   0x00000002   PAD
    0x08000214   0x000001e0   Code   RO          148    .text               system_stm32f10x.o
    0x080003f4   0x00000040   Code   RO          169    .text               startup_stm32f10x_hd.o
    0x08000434   0x000000dc   Code   RO          173    .text               misc.o
    0x08000510   0x0000035c   Code   RO          317    .text               stm32f10x_gpio.o
    0x0800086c   0x000003a4   Code   RO          365    .text               stm32f10x_rcc.o
    0x08000c10   0x00000e1a   Code   RO          415    .text               stm32f10x_tim.o
    0x08001a2a   0x00000002   PAD
    0x08001a2c   0x00000408   Code   RO          427    .text               stm32f10x_usart.o
    0x08001e34   0x00000a50   Code   RO          451    .text               line.o
    0x08002884   0x000001b4   Code   RO          466    .text               motor.o
    0x08002a38   0x000000d8   Code   RO          478    .text               sensor.o
    0x08002b10   0x000000e8   Code   RO          490    .text               delay.o
    0x08002bf8   0x00000430   Code   RO          521    .text               iic.o
    0x08003028   0x00000498   Code   RO          533    .text               usart.o
    0x080034c0   0x0000004e   Code   RO          551    .text               c_w.l(rt_memclr_w.o)
    0x0800350e   0x00000006   Code   RO          553    .text               c_w.l(heapauxi.o)
    0x08003514   0x0000004a   Code   RO          604    .text               c_w.l(sys_stackheap_outer.o)
    0x0800355e   0x00000012   Code   RO          606    .text               c_w.l(exit.o)
    0x08003570   0x00000008   Code   RO          614    .text               c_w.l(libspace.o)
    0x08003578   0x0000000c   Code   RO          675    .text               c_w.l(sys_exit.o)
    0x08003584   0x00000002   Code   RO          686    .text               c_w.l(use_no_semi.o)
    0x08003586   0x00000000   Code   RO          688    .text               c_w.l(indicate_semi.o)
    0x08003586   0x00000002   PAD
    0x08003588   0x000000c4   Code   RO          557    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x0800364c   0x00000184   Code   RO          564    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x080037d0   0x00000036   Code   RO          567    x$fpl$ffix          fz_ws.l(ffix.o)
    0x08003806   0x00000002   PAD
    0x08003808   0x0000003e   Code   RO          571    x$fpl$ffixu         fz_ws.l(ffixu.o)
    0x08003846   0x00000002   PAD
    0x08003848   0x00000030   Code   RO          576    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x08003878   0x00000026   Code   RO          575    x$fpl$ffltu         fz_ws.l(fflt_clz.o)
    0x0800389e   0x00000002   PAD
    0x080038a0   0x00000102   Code   RO          581    x$fpl$fmul          fz_ws.l(fmul.o)
    0x080039a2   0x0000008c   Code   RO          584    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x08003a2e   0x0000000a   Code   RO          586    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x08003a38   0x000000ea   Code   RO          559    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x08003b22   0x00000000   Code   RO          588    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x08003b22   0x00000002   PAD
    0x08003b24   0x00000020   Data   RO          741    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Base: 0x20000000, Size: 0x000006c8, Max: 0x00010000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00000014   Data   RW          149    .data               system_stm32f10x.o
    0x20000014   0x00000014   Data   RW          366    .data               stm32f10x_rcc.o
    0x20000028   0x0000001c   Data   RW          452    .data               line.o
    0x20000044   0x00000004   Data   RW          491    .data               delay.o
    0x20000048   0x00000001   Data   RW          535    .data               usart.o
    0x20000049   0x00000003   PAD
    0x2000004c   0x00000016   Zero   RW          534    .bss                usart.o
    0x20000062   0x00000002   PAD
    0x20000064   0x00000060   Zero   RW          615    .bss                c_w.l(libspace.o)
    0x200000c4   0x00000004   PAD
    0x200000c8   0x00000200   Zero   RW          167    HEAP                startup_stm32f10x_hd.o
    0x200002c8   0x00000400   Zero   RW          166    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         0          0          0          0          0         32   core_cm3.o
       232         22          0          4          0      17228   delay.o
      1072         14          0          0          0       3250   iic.o
      2640         56          0         28          0       3961   line.o
        48          0          0          0          0     253911   main.o
       220         22          0          0          0       1973   misc.o
       436         20          0          0          0       1057   motor.o
       216         18          0          0          0        992   sensor.o
        64         26        304          0       1536        860   startup_stm32f10x_hd.o
       860         38          0          0          0       5909   stm32f10x_gpio.o
        24          0          0          0          0       1214   stm32f10x_it.o
       932         36          0         20          0       9184   stm32f10x_rcc.o
      3610         88          0          0          0      23016   stm32f10x_tim.o
      1032         22          0          0          0       8632   stm32f10x_usart.o
       480         38          0         20          0       2091   system_stm32f10x.o
      1176         32          0          1         22       3797   usart.o

    ----------------------------------------------------------------------
     13046        <USER>        <GROUP>         76       1560     337107   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         4          0          0          3          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
       430          8          0          0          0        168   faddsub_clz.o
       388         76          0          0          0         96   fdiv.o
        54          4          0          0          0         84   ffix.o
        62          4          0          0          0         84   ffixu.o
        86          0          0          0          0        136   fflt_clz.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
         0          0          0          0          0          0   usenofp.o

    ----------------------------------------------------------------------
      1790        <USER>          <GROUP>          0        100       1468   Library Totals
        12          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       350         16          0          0         96        664   c_w.l
      1428        100          0          0          0        804   fz_ws.l

    ----------------------------------------------------------------------
      1790        <USER>          <GROUP>          0        100       1468   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     14836        548        336         76       1660     336803   Grand Totals
     14836        548        336         76       1660     336803   ELF Image Totals
     14836        548        336         76          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                15172 (  14.82kB)
    Total RW  Size (RW Data + ZI Data)              1736 (   1.70kB)
    Total ROM Size (Code + RO Data + RW Data)      15248 (  14.89kB)

==============================================================================

