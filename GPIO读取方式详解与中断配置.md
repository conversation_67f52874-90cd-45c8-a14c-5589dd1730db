# GPIO读取方式详解与MSPM0G3507中断配置方案

## 1. STM32原系统GPIO读取方式分析

### 1.1 **确认：STM32使用轮询方式，不是中断方式**

通过代码分析确认，STM32原系统使用的是**轮询(Polling)方式**，而不是中断方式：

#### **证据1：主循环无延时的连续轮询**
```c
// main.c - 主循环
while(1) {				
    track_zhixian1();    // 连续调用，无任何延时
//  track_zhixian2();		
//  track_PID1(40,0.05);
//  track_PID2(40,0.05);
//  track_PID3(40,0.05);	
}
```

#### **证据2：GPIO配置为普通输入模式**
```c
// sensor.c - GPIO配置
GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8|GPIO_Pin_9|GPIO_Pin_10|GPIO_Pin_11|
                              GPIO_Pin_12|GPIO_Pin_13|GPIO_Pin_14|GPIO_Pin_15;
GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;    // 上拉输入模式（非中断模式）
GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
GPIO_Init(GPIOE, &GPIO_InitStructure);
```

#### **证据3：直接寄存器读取方式**
```c
// sys.h - 位带操作宏定义
#define PEin(n)    BIT_ADDR(GPIOE_IDR_Addr,n)  // 直接读取IDR寄存器

// 展开后实际是：
// PEin(8) = *((volatile unsigned long *)(0x42300100 + (8<<2)))
// 直接访问GPIOE->IDR寄存器的第8位
```

#### **证据4：循迹函数中的实时读取**
```c
// line.c - track_zhixian1()函数
void track_zhixian1() {    
    // 每次调用都实时读取GPIO状态
    if     ((D1==1)&&(D2==0)&&(D3==0)&&...&&(D8==0)) motor(0,50);
    else if((D1==1)&&(D2==1)&&(D3==0)&&...&&(D8==0)) motor(10,50);
    // ... 15种状态判断，每次都重新读取
}

// sensor.h - 宏定义
#define D1 digtal(1)  // 每次调用都执行PEin(8)读取
```

### 1.2 **STM32轮询方式的特点**

#### **优点**：
- **实时性好**：无中断延迟，立即响应
- **逻辑简单**：无需中断服务程序
- **状态连续**：可以检测到所有状态变化
- **调试方便**：可以随时查看GPIO状态

#### **缺点**：
- **CPU占用高**：连续轮询消耗CPU资源
- **功耗较高**：CPU无法进入低功耗模式

## 2. MSPM0G3507两种实现方案

### 2.1 **方案一：轮询方式（推荐，与STM32一致）**

#### **SysConfig配置**
在SysConfig中配置GPIO：

**GPIO配置步骤**：
1. **添加GPIO模块**：在SysConfig中添加GPIO
2. **选择引脚**：选择PA0~PA7作为传感器输入
3. **配置参数**：
   - **Direction**: Input
   - **Internal Resistor**: Pull-up
   - **Hysteresis**: Disable
   - **Interrupt**: **Disable**（轮询方式不需要中断）
   - **Wakeup**: Disable

#### **SysConfig生成的代码**
```c
// ti_msp_dl_config.c - SysConfig生成
static const DL_GPIO_initDigitalInputPinConfig gGPIOA_inputPinConfig[] = {
    {.pin = DL_GPIO_PIN_0, .mode = DL_GPIO_INPUT, .resistor = DL_GPIO_RESISTOR_PULL_UP},
    {.pin = DL_GPIO_PIN_1, .mode = DL_GPIO_INPUT, .resistor = DL_GPIO_RESISTOR_PULL_UP},
    {.pin = DL_GPIO_PIN_2, .mode = DL_GPIO_INPUT, .resistor = DL_GPIO_RESISTOR_PULL_UP},
    {.pin = DL_GPIO_PIN_3, .mode = DL_GPIO_INPUT, .resistor = DL_GPIO_RESISTOR_PULL_UP},
    {.pin = DL_GPIO_PIN_4, .mode = DL_GPIO_INPUT, .resistor = DL_GPIO_RESISTOR_PULL_UP},
    {.pin = DL_GPIO_PIN_5, .mode = DL_GPIO_INPUT, .resistor = DL_GPIO_RESISTOR_PULL_UP},
    {.pin = DL_GPIO_PIN_6, .mode = DL_GPIO_INPUT, .resistor = DL_GPIO_RESISTOR_PULL_UP},
    {.pin = DL_GPIO_PIN_7, .mode = DL_GPIO_INPUT, .resistor = DL_GPIO_RESISTOR_PULL_UP},
};

void SYSCFG_DL_GPIO_init(void) {
    DL_GPIO_initDigitalInputPinGroup(GPIOA, gGPIOA_inputPinConfig, 8);
}
```

#### **应用层实现**
```c
// sensor.h - 端口定义
#define SENSOR_GPIO_PORT    GPIOA
#define SENSOR_PIN_MASK     0xFF  // PA0~PA7

// sensor.c - 初始化函数
void SENSOR_GPIO_Config(void) {
    SYSCFG_DL_GPIO_init();  // SysConfig生成的初始化
}

// sensor.c - 轮询读取函数（与STM32完全一致）
uint8_t digtal(uint8_t channel) {
    uint8_t value = 0;
    uint32_t pin_mask;
    
    if (channel >= 1 && channel <= 8) {
        pin_mask = 1 << (channel - 1);
        
        // 直接读取GPIO状态（轮询方式）
        if (DL_GPIO_readPins(SENSOR_GPIO_PORT, pin_mask) != 0) {
            value = 1;  // 高电平 - 白色表面
        } else {
            value = 0;  // 低电平 - 黑线
        }
    }
    
    return value;
}

// sensor.h - 宏定义（与STM32完全一致）
#define D1 digtal(1)  // PA0
#define D2 digtal(2)  // PA1
#define D3 digtal(3)  // PA2
#define D4 digtal(4)  // PA3
#define D5 digtal(5)  // PA4
#define D6 digtal(6)  // PA5
#define D7 digtal(7)  // PA6
#define D8 digtal(8)  // PA7
```

#### **主程序（与STM32完全一致）**
```c
// main.c
int main(void) {
    SYSCFG_DL_init();
    SENSOR_GPIO_Config();
    motor_init();
    
    motor_control(0, 0);
    delay_ms(1000);
    
    while (1) {
        track_zhixian1();    // 连续轮询，与STM32一致
    }
}
```

### 2.2 **方案二：中断方式（优化方案，降低功耗）**

#### **SysConfig中断配置**

**GPIO中断配置步骤**：
1. **添加GPIO模块**：在SysConfig中添加GPIO
2. **选择引脚**：选择PA0~PA7作为传感器输入
3. **配置参数**：
   - **Direction**: Input
   - **Internal Resistor**: Pull-up
   - **Hysteresis**: Disable
   - **Interrupt**: **Enable**
   - **Interrupt Condition**: **Rising and Falling Edge**（上升沿和下降沿）
   - **Wakeup**: Enable（如果需要低功耗唤醒）

#### **SysConfig中断配置代码**
```c
// ti_msp_dl_config.c - SysConfig生成的中断配置
static const DL_GPIO_initDigitalInputPinConfig gGPIOA_inputPinConfig[] = {
    {
        .pin = DL_GPIO_PIN_0,
        .mode = DL_GPIO_INPUT,
        .resistor = DL_GPIO_RESISTOR_PULL_UP,
        .hysteresis = DL_GPIO_HYSTERESIS_DISABLE,
        .wakeupConfig = DL_GPIO_WAKEUP_ON_0_TO_1 | DL_GPIO_WAKEUP_ON_1_TO_0
    },
    // ... PA1~PA7类似配置
};

void SYSCFG_DL_GPIO_init(void) {
    // 配置GPIO输入
    DL_GPIO_initDigitalInputPinGroup(GPIOA, gGPIOA_inputPinConfig, 8);
    
    // 使能GPIO中断
    DL_GPIO_enableInterrupt(GPIOA, SENSOR_PIN_MASK);
    
    // 配置中断优先级
    NVIC_SetPriority(GPIOA_INT_IRQn, 1);
    NVIC_EnableIRQ(GPIOA_INT_IRQn);
}
```

#### **中断服务程序实现**
```c
// sensor.c - 全局变量
static volatile uint8_t g_sensor_state = 0;      // 当前传感器状态
static volatile bool g_sensor_changed = false;   // 状态变化标志

// 中断服务程序
void GPIOA_IRQHandler(void) {
    uint32_t interrupt_status = DL_GPIO_getEnabledInterruptStatus(GPIOA, SENSOR_PIN_MASK);
    
    if (interrupt_status != 0) {
        // 读取当前传感器状态
        g_sensor_state = (uint8_t)(DL_GPIO_readPins(GPIOA, SENSOR_PIN_MASK) & 0xFF);
        g_sensor_changed = true;
        
        // 清除中断标志
        DL_GPIO_clearInterruptStatus(GPIOA, interrupt_status);
    }
}

// 获取传感器状态（中断方式）
uint8_t get_sensor_state(void) {
    return g_sensor_state;
}

// 检查状态是否变化
bool is_sensor_changed(void) {
    if (g_sensor_changed) {
        g_sensor_changed = false;  // 清除标志
        return true;
    }
    return false;
}

// 单路传感器读取（中断方式）
uint8_t digtal(uint8_t channel) {
    if (channel >= 1 && channel <= 8) {
        return (g_sensor_state >> (channel - 1)) & 0x01;
    }
    return 0;
}
```

#### **中断方式的循迹算法**
```c
// line.c - 中断驱动的循迹算法
void track_zhixian1_interrupt(void) {
    static unsigned char lukou_num = 0;
    
    // 只有传感器状态变化时才处理
    if (!is_sensor_changed()) {
        return;  // 无变化，直接返回
    }
    
    uint8_t sensor_state = get_sensor_state();
    
    // 路口检测
    uint8_t active_count = 0;
    uint8_t temp = sensor_state;
    while (temp) {
        active_count += temp & 1;
        temp >>= 1;
    }
    
    if (active_count >= 4) {
        lukou_num++;
        if (lukou_num >= 2) {
            lukou_num = 0;
            motor_control(0, 0);
            delay_ms(2000);
            return;
        }
    }
    
    // 循迹控制（使用位操作优化）
    switch (sensor_state) {
        case 0x01: motor_control(0, 50); break;    // 0000 0001
        case 0x03: motor_control(10, 50); break;   // 0000 0011
        case 0x02: motor_control(15, 40); break;   // 0000 0010
        case 0x06: motor_control(20, 40); break;   // 0000 0110
        case 0x04: motor_control(25, 40); break;   // 0000 0100
        case 0x0C: motor_control(35, 40); break;   // 0000 1100
        case 0x08: motor_control(35, 40); break;   // 0000 1000
        case 0x18: motor_control(40, 40); break;   // 0001 1000 - 中心
        case 0x10: motor_control(40, 35); break;   // 0001 0000
        case 0x30: motor_control(40, 35); break;   // 0011 0000
        case 0x20: motor_control(40, 25); break;   // 0010 0000
        case 0x60: motor_control(40, 20); break;   // 0110 0000
        case 0x40: motor_control(40, 15); break;   // 0100 0000
        case 0xC0: motor_control(50, 10); break;   // 1100 0000
        case 0x80: motor_control(50, 0); break;    // 1000 0000
        default: motor_control(20, 20); break;     // 默认前进
    }
}
```

#### **中断方式的主程序**
```c
// main.c - 中断方式主程序
int main(void) {
    SYSCFG_DL_init();
    SENSOR_GPIO_Config();
    motor_init();
    
    motor_control(0, 0);
    delay_ms(1000);
    
    // 初始读取一次传感器状态
    g_sensor_state = (uint8_t)(DL_GPIO_readPins(GPIOA, SENSOR_PIN_MASK) & 0xFF);
    
    while (1) {
        track_zhixian1_interrupt();  // 中断驱动的循迹
        
        // CPU可以进入低功耗模式等待中断
        // DL_SYSCTL_enableSleepOnExit();
        // __WFI();  // 等待中断
    }
}
```

## 3. 两种方案对比分析

### 3.1 **性能对比**

| 特性 | 轮询方式 | 中断方式 |
|------|---------|---------|
| **响应时间** | 立即响应 | 中断延迟（微秒级） |
| **CPU占用** | 高（连续轮询） | 低（事件驱动） |
| **功耗** | 高 | 低（可进入休眠） |
| **实现复杂度** | 简单 | 中等 |
| **调试难度** | 容易 | 较难（中断调试） |
| **兼容性** | 与STM32完全一致 | 需要修改算法逻辑 |

### 3.2 **适用场景**

#### **轮询方式适用于**：
- **高速循迹**：需要最快响应时间
- **简单调试**：开发和调试阶段
- **完全兼容**：要求与STM32行为完全一致
- **电源充足**：不考虑功耗问题

#### **中断方式适用于**：
- **电池供电**：需要低功耗运行
- **多任务系统**：CPU需要处理其他任务
- **长期运行**：减少系统发热
- **复杂应用**：集成更多功能模块

## 4. SysConfig详细配置步骤

### 4.1 **轮询方式SysConfig配置**

#### **步骤1：添加GPIO模块**
1. 打开SysConfig
2. 点击"+"添加外设
3. 选择"GPIO" → "Digital Input"

#### **步骤2：配置GPIO引脚**
```
Pin Configuration:
├── Pin: PA0, PA1, PA2, PA3, PA4, PA5, PA6, PA7
├── Direction: Input
├── Internal Resistor: Pull-up
├── Hysteresis: Disable
├── Interrupt: Disable  ← 轮询方式关键设置
└── Wakeup: Disable
```

#### **步骤3：生成代码**
点击"Generate"生成配置代码

### 4.2 **中断方式SysConfig配置**

#### **步骤1：添加GPIO模块**
同轮询方式

#### **步骤2：配置GPIO引脚**
```
Pin Configuration:
├── Pin: PA0, PA1, PA2, PA3, PA4, PA5, PA6, PA7
├── Direction: Input
├── Internal Resistor: Pull-up
├── Hysteresis: Disable
├── Interrupt: Enable  ← 中断方式关键设置
├── Interrupt Condition: Rising and Falling Edge
├── Priority: 1
└── Wakeup: Enable (可选)
```

#### **步骤3：配置中断控制器**
```
NVIC Configuration:
├── GPIOA Interrupt: Enable
├── Priority: 1
└── Preemption Priority: 0
```

#### **步骤4：生成代码**
点击"Generate"生成配置代码和中断服务程序模板

## 5. 推荐方案与实施建议

### 5.1 **推荐方案：轮询方式**

**理由**：
1. **完全兼容**：与STM32原系统行为完全一致
2. **调试简单**：可以直接移植STM32的调试方法
3. **响应最快**：无中断延迟，适合高速循迹
4. **实现简单**：无需修改原有算法逻辑

### 5.2 **实施步骤**

#### **第一阶段：基础移植（轮询方式）**
1. 使用SysConfig配置GPIO为普通输入模式
2. 实现`digtal()`函数，保持与STM32一致的接口
3. 移植`track_zhixian1()`函数，保持算法逻辑不变
4. 测试验证功能正确性

#### **第二阶段：性能优化（可选）**
1. 实现批量读取函数`read_all_sensors()`
2. 优化循迹算法，使用位操作替代多次if判断
3. 添加性能监控和调试功能

#### **第三阶段：功耗优化（可选）**
1. 如果需要低功耗，再考虑实现中断方式
2. 添加低功耗模式和唤醒机制
3. 优化中断服务程序的执行效率

### 5.3 **代码模板**

#### **完整的轮询方式实现**
```c
// sensor.h
#ifndef __SENSOR_H
#define __SENSOR_H

#include "ti_msp_dl_config.h"

#define SENSOR_GPIO_PORT    GPIOA
#define SENSOR_PIN_MASK     0xFF

void SENSOR_GPIO_Config(void);
uint8_t digtal(uint8_t channel);
uint8_t read_all_sensors(void);

// 宏定义（与STM32完全一致）
#define D1 digtal(1)
#define D2 digtal(2)
#define D3 digtal(3)
#define D4 digtal(4)
#define D5 digtal(5)
#define D6 digtal(6)
#define D7 digtal(7)
#define D8 digtal(8)

#endif
```

```c
// sensor.c
#include "sensor.h"

void SENSOR_GPIO_Config(void) {
    SYSCFG_DL_GPIO_init();  // SysConfig生成的初始化
}

uint8_t digtal(uint8_t channel) {
    uint8_t value = 0;
    uint32_t pin_mask;
    
    if (channel >= 1 && channel <= 8) {
        pin_mask = 1 << (channel - 1);
        if (DL_GPIO_readPins(SENSOR_GPIO_PORT, pin_mask) != 0) {
            value = 1;
        } else {
            value = 0;
        }
    }
    
    return value;
}

uint8_t read_all_sensors(void) {
    return (uint8_t)(DL_GPIO_readPins(SENSOR_GPIO_PORT, SENSOR_PIN_MASK) & 0xFF);
}
```

## 6. 总结

**STM32原系统使用轮询方式**，通过位带操作直接读取GPIO寄存器，在主循环中连续调用循迹函数。

**MSPM0G3507移植建议**：
1. **首选轮询方式**：完全兼容STM32，实现简单，调试方便
2. **SysConfig配置**：GPIO输入模式，上拉电阻，**不使能中断**
3. **可选中断方式**：如果需要低功耗或多任务，可以实现中断驱动版本

**关键配置**：
- **轮询方式**：SysConfig中GPIO Interrupt设置为**Disable**
- **中断方式**：SysConfig中GPIO Interrupt设置为**Enable**，选择**Rising and Falling Edge**

这样既保证了与原系统的兼容性，又提供了优化的可能性。