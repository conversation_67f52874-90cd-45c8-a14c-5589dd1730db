# 轮询方式详细解释 - 通俗易懂版

## 1. 什么是轮询方式？

### 1.1 **生活中的轮询例子**

想象你在等外卖：

**轮询方式**：你每隔30秒就看一次手机，检查外卖到了没有
- 不停地主动查看
- 自己控制检查频率
- 立即知道状态变化

**中断方式**：外卖员到了给你打电话通知
- 被动等待通知
- 外卖员控制通知时机
- 有通知延迟

### 1.2 **程序中的轮询**

**轮询方式**就是CPU**主动、反复、连续**地去检查某个状态，就像：
```
while(1) {
    检查传感器状态();
    根据状态做出反应();
}
```

## 2. STM32代码详细解析

### 2.1 **主循环 - 轮询的核心**

```c
// main.c - 第28-35行
while(1) {				
    track_zhixian1();    // ← 这就是轮询！不停地调用这个函数
//  track_zhixian2();		
//  track_PID1(40,0.05);
//  track_PID2(40,0.05);
//  track_PID3(40,0.05);	
}
```

**解释**：
- `while(1)` = 无限循环，永远不停止
- 没有任何`delay()`延时函数
- CPU以最快速度反复执行`track_zhixian1()`
- 大概每秒执行几万次到几十万次（取决于CPU速度）

### 2.2 **传感器读取函数 - 轮询的执行者**

```c
// sensor.c - digtal()函数详解
unsigned char digtal(unsigned char channel) {  // 读取1~8通道
    u8 value = 0;
    switch(channel) {
        case 1:  
            if(PEin(8) == 1) value = 1;  // ← 关键：直接读取GPIO引脚
            else value = 0;  
            break;  
        case 2: 
            if(PEin(9) == 1) value = 1;  // ← 关键：直接读取GPIO引脚
            else value = 0;  
            break;  
        // ... 其他通道类似
    }
    return value; 
}
```

**解释**：
- `PEin(8)` = 直接读取PE8引脚的电平状态
- **没有等待**，**没有中断**，**立即返回**当前状态
- 每次调用都是**实时读取**硬件寄存器

### 2.3 **PEin宏的底层实现**

```c
// sys.h - 宏定义展开过程
#define PEin(n)    BIT_ADDR(GPIOE_IDR_Addr,n)  // 第45行

// 进一步展开：
#define BIT_ADDR(addr, bitnum)   MEM_ADDR(BITBAND(addr, bitnum))  // 第11行
#define MEM_ADDR(addr)  *((volatile unsigned long  *)(addr))      // 第10行
#define BITBAND(addr, bitnum) ((addr & 0xF0000000)+0x2000000+((addr &0xFFFFF)<<5)+(bitnum<<2))  // 第9行

// GPIOE_IDR_Addr定义：
#define GPIOE_IDR_Addr    (GPIOE_BASE+8) //0x40011808  // 第26行
```

**完全展开后，PEin(8)实际是**：
```c
PEin(8) = *((volatile unsigned long *)(0x42300100 + (8<<2)))
```

**这行代码的含义**：
- `0x42300100` = 位带操作的内存地址
- `(8<<2)` = 8×4 = 32，因为每个位占4字节
- `*((volatile unsigned long *)(...))` = 直接读取内存地址的值
- **结果**：直接读取GPIOE第8个引脚的状态（0或1）

### 2.4 **宏定义简化访问**

```c
// sensor.h - 宏定义
#define D1 digtal(1)  // PE8
#define D2 digtal(2)  // PE9
#define D3 digtal(3)  // PE10
#define D4 digtal(4)  // PE11
#define D5 digtal(5)  // PE12
#define D6 digtal(6)  // PE13
#define D7 digtal(7)  // PE14
#define D8 digtal(8)  // PE15
```

**解释**：
- 每次使用`D1`时，实际执行`digtal(1)`
- 每次执行`digtal(1)`时，实际执行`PEin(8)`
- 每次执行`PEin(8)`时，实际读取硬件寄存器

## 3. 循迹算法中的轮询过程

### 3.1 **循迹函数的轮询逻辑**

```c
// line.c - track_zhixian1()函数详解
void track_zhixian1() {    
    unsigned char num = 0, i;  
    
    // 第一部分：路口检测（轮询所有传感器）
    for(i=0; i<2; i++) {  // 连续检测2次
        if(D1 == 1) num++;  // ← 轮询：读取D1状态
        if(D2 == 1) num++;  // ← 轮询：读取D2状态
        if(D3 == 1) num++;  // ← 轮询：读取D3状态
        if(D4 == 1) num++;  // ← 轮询：读取D4状态
        if(D5 == 1) num++;  // ← 轮询：读取D5状态
        if(D6 == 1) num++;  // ← 轮询：读取D6状态
        if(D7 == 1) num++;  // ← 轮询：读取D7状态
        if(D8 == 1) num++;  // ← 轮询：读取D8状态
        
        if(num >= (ADC_N-4)) {  // 如果4个以上传感器激活
            lukou_num++; 
            if(lukou_num == 1) Delay_ms(10);
        }  
        num = 0;
    }
    
    // 第二部分：循迹控制（轮询判断状态组合）
    if     ((D1==1)&&(D2==0)&&(D3==0)&&(D4==0)&&(D5==0)&&(D6==0)&&(D7==0)&&(D8==0)) 
        motor(0,50);   // 最左侧：左轮停止，右轮50速度
    else if((D1==1)&&(D2==1)&&(D3==0)&&(D4==0)&&(D5==0)&&(D6==0)&&(D7==0)&&(D8==0)) 
        motor(10,50);  // 左侧：左轮10速度，右轮50速度
    else if((D1==0)&&(D2==1)&&(D3==0)&&(D4==0)&&(D5==0)&&(D6==0)&&(D7==0)&&(D8==0)) 
        motor(15,40);  // 左偏：左轮15速度，右轮40速度
    // ... 总共15种状态组合
    else 
        motor(20,20);  // 默认：直行
}
```

### 3.2 **每次函数调用的执行过程**

**第1步**：路口检测
```
读取D1 → 读取D2 → 读取D3 → 读取D4 → 读取D5 → 读取D6 → 读取D7 → 读取D8
计算激活传感器数量 → 判断是否为路口
```

**第2步**：状态判断
```
同时读取D1~D8 → 组成8位状态码 → 查找对应的电机控制动作
```

**第3步**：电机控制
```
调用motor(左轮速度, 右轮速度) → 控制小车转向
```

**第4步**：函数结束，返回主循环，立即再次调用

## 4. 轮询的时间特性

### 4.1 **执行频率估算**

假设STM32运行在72MHz：

```c
// 单次digtal()函数执行时间估算
unsigned char digtal(unsigned char channel) {
    u8 value = 0;                    // 1个时钟周期
    switch(channel) {                // 2-3个时钟周期
        case 1:  
            if(PEin(8) == 1)         // 5-10个时钟周期（寄存器读取）
                value = 1;           // 1个时钟周期
            else 
                value = 0;           // 1个时钟周期
            break;                   // 1个时钟周期
    }
    return value;                    // 1个时钟周期
}
// 总计：约10-20个时钟周期
```

**频率计算**：
- 单次`digtal()`调用：约20个时钟周期 = 20/72MHz ≈ 0.28微秒
- 单次`track_zhixian1()`调用：约读取16次传感器 + 判断逻辑 ≈ 10微秒
- **轮询频率**：1/10微秒 = **100,000次/秒** = **10万Hz**

### 4.2 **实际执行时序图**

```
时间轴：  0μs    10μs   20μs   30μs   40μs   50μs
         │      │      │      │      │      │
主循环：  ├─track─┤─track─┤─track─┤─track─┤─track─┤
         │      │      │      │      │      │
传感器：  读8次   读8次   读8次   读8次   读8次   ...
         │      │      │      │      │      │
电机：    控制   控制   控制   控制   控制   ...
```

**特点**：
- **连续不断**：没有空闲时间
- **实时响应**：传感器状态变化后10微秒内就能响应
- **高频更新**：电机控制信号每10微秒更新一次

## 5. 轮询方式的优缺点

### 5.1 **优点**

#### **响应速度快**
```c
传感器状态变化 → 10微秒内检测到 → 立即控制电机
```

#### **逻辑简单**
```c
while(1) {
    读传感器();
    控制电机();
}
```

#### **状态连续**
- 不会错过任何状态变化
- 可以检测到瞬间的状态

#### **调试方便**
- 可以随时暂停查看传感器状态
- 逻辑流程清晰

### 5.2 **缺点**

#### **CPU占用率100%**
```c
while(1) {  // CPU永远在这个循环里，无法做其他事情
    track_zhixian1();
}
```

#### **功耗高**
- CPU始终满负荷运行
- 无法进入低功耗模式

#### **无法多任务**
- 无法同时处理其他功能（如蓝牙、显示等）

## 6. 与中断方式的对比

### 6.1 **轮询方式执行流程**
```
主循环开始
    ↓
读取传感器1 → 读取传感器2 → ... → 读取传感器8
    ↓
判断状态组合
    ↓
控制电机
    ↓
回到主循环开始（立即重复）
```

### 6.2 **中断方式执行流程**
```
主循环等待
    ↓
传感器状态变化 → 触发中断
    ↓
中断服务程序：读取所有传感器 → 判断状态 → 控制电机
    ↓
返回主循环等待
```

### 6.3 **代码对比**

**轮询方式**：
```c
int main(void) {
    初始化();
    while(1) {
        track_zhixian1();  // 连续执行
    }
}
```

**中断方式**：
```c
int main(void) {
    初始化();
    使能中断();
    while(1) {
        __WFI();  // 等待中断，CPU休眠
    }
}

void GPIO_IRQHandler(void) {  // 中断服务程序
    track_zhixian1();  // 只在状态变化时执行
}
```

## 7. MSPM0G3507轮询实现

### 7.1 **完全对应的轮询实现**

```c
// sensor.c - MSPM0G3507版本
uint8_t digtal(uint8_t channel) {
    uint8_t value = 0;
    uint32_t pin_mask;
    
    if (channel >= 1 && channel <= 8) {
        pin_mask = 1 << (channel - 1);  // 计算引脚掩码
        
        // 关键：直接读取GPIO寄存器（轮询方式）
        if (DL_GPIO_readPins(SENSOR_GPIO_PORT, pin_mask) != 0) {
            value = 1;  // 高电平
        } else {
            value = 0;  // 低电平
        }
    }
    
    return value;
}

// main.c - MSPM0G3507版本
int main(void) {
    SYSCFG_DL_init();
    SENSOR_GPIO_Config();
    motor_init();
    
    while (1) {
        track_zhixian1();  // 与STM32完全一样的轮询
    }
}
```

### 7.2 **底层寄存器对比**

**STM32**：
```c
PEin(8) → 直接读取GPIOE->IDR寄存器的第8位
```

**MSPM0G3507**：
```c
DL_GPIO_readPins(GPIOA, DL_GPIO_PIN_0) → 直接读取GPIOA->DIN寄存器的第0位
```

**本质相同**：都是直接读取GPIO输入数据寄存器

## 8. 总结

### 8.1 **轮询方式的本质**

**轮询 = CPU主动、连续、不停地检查状态**

就像：
- 你不停地刷新网页看有没有新消息
- 你每隔几秒就看一次手表
- 你反复检查水开了没有

### 8.2 **STM32代码的轮询特征**

1. **主循环无延时**：`while(1) { track_zhixian1(); }`
2. **直接读寄存器**：`PEin(8)` → 位带操作直读IDR
3. **实时状态检测**：每次调用都重新读取
4. **高频执行**：约10万次/秒的检测频率

### 8.3 **移植要点**

**保持轮询特性**：
- MSPM0G3507也用`while(1)`无限循环
- 用`DL_GPIO_readPins()`直接读取寄存器
- 保持相同的高频检测特性
- SysConfig中**不使能GPIO中断**

老板，轮询方式就是CPU像"勤劳的蜜蜂"一样，不停地主动检查传感器状态，一刻不停歇。这种方式响应最快，逻辑最简单，但也最耗电。STM32原系统就是这样工作的！