# STM32 8路灰度传感器工作原理分析

## 1. 系统概述

这是一个基于STM32F103的8路数字量灰度传感器循迹系统，主要用于智能小车的黑线循迹功能。

## 2. 硬件架构

### 2.1 核心处理器
- **MCU**: STM32F103系列（高密度版本）
- **时钟**: 72MHz系统时钟
- **Flash**: 256KB以上

### 2.2 传感器接口
- **传感器数量**: 8路数字量灰度传感器
- **接口类型**: 数字GPIO输入 + IIC通信 + USART通信
- **GPIO端口**: GPIOE的Pin8-Pin15（PE8~PE15）
- **输入模式**: 上拉输入（GPIO_Mode_IPU）

### 2.3 通信接口
- **IIC接口**: PB6(SCL), PB7(SDA) - 用于读取传感器模块数据
- **USART3**: PC10(TX), PC11(RX) - 用于与传感器模块通信
- **波特率**: 115200

## 3. 关键信号传输机制

### 3.1 数字量GPIO读取方式
```c
// 传感器GPIO配置 - 8路数字输入
GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8|GPIO_Pin_9|GPIO_Pin_10|GPIO_Pin_11|
                              GPIO_Pin_12|GPIO_Pin_13|GPIO_Pin_14|GPIO_Pin_15;
GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;  // 上拉输入
```

**信号特征**:
- **高电平(1)**: 检测到白色/浅色表面（反射光强）
- **低电平(0)**: 检测到黑色/深色表面（吸收光线）
- **读取函数**: `digtal(channel)` - 返回0或1

### 3.2 IIC通信方式
**通信协议**:
- **从机地址**: 0x57 (默认传感器模块地址)
- **寄存器地址**:
  - 0xA0: 数字量数据寄存器
  - 0xA1: 位置+偏差数据寄存器  
  - 0xA2: 模拟量数据寄存器

**数据格式**:
```c
// 读取数字量数据 (1字节)
Read_IICData1(0x01, Data);  // 返回8位数字量状态

// 读取位置偏差数据 (3字节)
Read_IICData2(0x01, Data);  // 返回位置信息+偏差值

// 读取模拟量数据 (16字节)
Read_IICData3(0x01, Data);  // 返回8路模拟量值
```

### 3.3 USART通信方式
**通信协议**:
- **帧头**: 0x57
- **地址**: 传感器模块地址
- **数据长度**: 根据请求类型变化
- **帧尾**: 校验字节

**数据包格式**:
```
发送: [0x57] [ADDR]
接收: [DATA...] [CHECKSUM]
```

## 4. 循迹算法实现

### 4.1 基础数字量循迹 (track_zhixian1)
```c
// 8路传感器状态判断
if((D1==1)&&(D2==0)&&...&&(D8==0))  motor(0,50);   // 最左侧
if((D1==0)&&(D2==0)&&...&&(D8==1))  motor(50,0);   // 最右侧
if((D1==0)&&...&&(D4==1)&&(D5==1)&&...&&(D8==0))  motor(40,40); // 中心位置
```

### 4.2 PID控制循迹 (track_PID1/2/3)
- **比例控制**: P系数调节转向强度
- **偏差计算**: 基于传感器位置加权平均
- **速度限制**: 防止电机速度过高或过低

## 5. 移植到TI MSP430G3507 (CCS环境)

### 5.1 硬件映射

#### GPIO配置
```c
// MSP430G3507 GPIO配置
// 8路传感器输入 - 使用P1.0-P1.7
P1DIR &= ~(BIT0 + BIT1 + BIT2 + BIT3 + BIT4 + BIT5 + BIT6 + BIT7);  // 输入模式
P1REN |= (BIT0 + BIT1 + BIT2 + BIT3 + BIT4 + BIT5 + BIT6 + BIT7);   // 使能上拉电阻
P1OUT |= (BIT0 + BIT1 + BIT2 + BIT3 + BIT4 + BIT5 + BIT6 + BIT7);   // 上拉
```

#### I2C配置 (使用USCI_B0)
```c
// I2C主机模式配置
UCB0CTL1 |= UCSWRST;                    // 软件复位
UCB0CTL0 = UCMST + UCMODE_3 + UCSYNC;   // I2C主机，同步模式
UCB0CTL1 = UCSSEL_2 + UCSWRST;          // SMCLK时钟源
UCB0BR0 = 12;                           // 时钟分频
UCB0BR1 = 0;
UCB0I2CSA = 0x57;                       // 从机地址
UCB0CTL1 &= ~UCSWRST;                   // 清除复位
```

#### UART配置 (使用USCI_A0)
```c
// UART配置 - 115200波特率
UCA0CTL1 |= UCSWRST;                    // 软件复位
UCA0CTL1 |= UCSSEL_2;                   // SMCLK
UCA0BR0 = 8;                            // 1MHz/115200 ≈ 8.68
UCA0BR1 = 0;
UCA0MCTL = UCBRS2 + UCBRS0;             // 调制
UCA0CTL1 &= ~UCSWRST;                   // 清除复位
```

### 5.2 SysConfig配置要点

#### 时钟配置
```c
// 系统时钟配置
DCOCTL = 0;                             // 选择最低DCOx和MODx设置
BCSCTL1 = CALBC1_1MHZ;                  // 设置DCO为1MHz
DCOCTL = CALDCO_1MHZ;
```

#### 引脚复用配置
```c
// I2C引脚配置
P1SEL |= BIT6 + BIT7;                   // P1.6=UCB0SDA, P1.7=UCB0SCL
P1SEL2 |= BIT6 + BIT7;

// UART引脚配置  
P1SEL |= BIT1 + BIT2;                   // P1.1=UCA0RXD, P1.2=UCA0TXD
P1SEL2 |= BIT1 + BIT2;
```

### 5.3 关键底层驱动实现

#### 数字量读取函数
```c
uint8_t read_sensor_digital(uint8_t channel) {
    if (channel >= 1 && channel <= 8) {
        return (P1IN & (1 << (channel-1))) ? 1 : 0;
    }
    return 0;
}
```

#### I2C读取函数
```c
void read_sensor_i2c(uint8_t reg_addr, uint8_t* data, uint8_t length) {
    UCB0I2CSA = 0x57;                   // 设置从机地址
    
    // 发送寄存器地址
    UCB0CTL1 |= UCTR + UCTXSTT;         // 发送模式，发送起始位
    while (UCB0CTL1 & UCTXSTT);        // 等待起始位发送完成
    UCB0TXBUF = reg_addr;               // 发送寄存器地址
    while (!(IFG2 & UCB0TXIFG));       // 等待发送完成
    
    // 切换到接收模式
    UCB0CTL1 &= ~UCTR;                  // 接收模式
    UCB0CTL1 |= UCTXSTT;                // 发送重新起始位
    
    // 接收数据
    for (int i = 0; i < length; i++) {
        if (i == length - 1) {
            UCB0CTL1 |= UCTXSTP;        // 最后一个字节后发送停止位
        }
        while (!(IFG2 & UCB0RXIFG));   // 等待接收完成
        data[i] = UCB0RXBUF;            // 读取数据
    }
}
```

#### UART通信函数
```c
void uart_send_byte(uint8_t data) {
    while (!(IFG2 & UCA0TXIFG));       // 等待发送缓冲区空
    UCA0TXBUF = data;                   // 发送数据
}

uint8_t uart_receive_byte(void) {
    while (!(IFG2 & UCA0RXIFG));       // 等待接收完成
    return UCA0RXBUF;                   // 返回接收数据
}

void read_sensor_uart(uint8_t addr, uint32_t* data) {
    uart_send_byte(0x57);               // 发送帧头
    uart_send_byte(addr);               // 发送地址
    
    // 接收响应数据
    uint8_t response[10];
    for (int i = 0; i < 3; i++) {       // 根据数据类型调整长度
        response[i] = uart_receive_byte();
    }
    
    // 解析数据
    if (response[2] == 0x02) {          // 校验帧尾
        *data = response[1];            // 提取有效数据
    }
}
```

### 5.4 接口适配层

#### 传感器抽象接口
```c
typedef struct {
    uint8_t (*read_digital)(uint8_t channel);
    void (*read_i2c_data)(uint8_t reg, uint8_t* data, uint8_t len);
    void (*read_uart_data)(uint8_t addr, uint32_t* data);
} sensor_interface_t;

// 接口实现
sensor_interface_t sensor_if = {
    .read_digital = read_sensor_digital,
    .read_i2c_data = read_sensor_i2c,
    .read_uart_data = read_sensor_uart
};
```

#### 循迹算法移植
```c
void track_line_basic(void) {
    uint8_t sensor_state = 0;
    
    // 读取8路传感器状态
    for (int i = 0; i < 8; i++) {
        if (sensor_if.read_digital(i+1)) {
            sensor_state |= (1 << i);
        }
    }
    
    // 根据传感器状态控制电机
    switch (sensor_state) {
        case 0x01: motor_control(0, 50); break;    // 最左
        case 0x80: motor_control(50, 0); break;    // 最右
        case 0x18: motor_control(40, 40); break;  // 中心
        // ... 其他状态
        default: motor_control(20, 20); break;    // 默认前进
    }
}
```

## 6. 移植注意事项

### 6.1 硬件差异
- **时钟频率**: MSP430G3507最高16MHz，需要调整延时和通信波特率
- **GPIO数量**: MSP430引脚较少，需要合理分配
- **内存限制**: MSP430 RAM较小，需要优化数据结构

### 6.2 软件适配
- **中断向量**: MSP430中断向量表与STM32不同
- **寄存器操作**: 需要使用MSP430特定的寄存器操作方式
- **编译器**: CCS使用TI编译器，语法可能有差异

### 6.3 性能优化
- **算法简化**: 可能需要简化PID算法以适应较低的处理能力
- **数据精度**: 根据实际需求调整数据精度
- **功耗管理**: MSP430具有优秀的低功耗特性，可以利用

## 7. 总结

STM32版本的灰度传感器系统通过三种通信方式（GPIO、I2C、UART）实现了灵活的传感器数据获取，移植到MSP430G3507时需要重点关注硬件接口适配和性能优化。关键是保持传感器数据获取的实时性和循迹算法的稳定性。