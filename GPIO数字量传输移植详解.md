# GPIO数字量传输方式详细移植方案

## 1. 确认：确实是三种通信方式

通过代码分析确认，STM32系统确实支持**三种通信方式**：

### 1.1 三种通信方式对比
| 通信方式 | 使用函数 | 当前状态 | 数据获取方式 |
|---------|---------|---------|-------------|
| **GPIO数字量** | `track_zhixian1()` | ✅ **正在使用** | 直接读取PE8~PE15 |
| **UART通信** | `track_zhixian2()`, `track_PID1/2/3()` | ❌ 注释掉 | 通过USART3协议通信 |
| **IIC通信** | `Read_IICData1/2/3()` | ❌ 注释掉 | 通过I2C协议通信 |

### 1.2 主程序使用情况
```c
while(1) {				
    track_zhixian1();        // ✅ 当前使用：GPIO数字量方式
//  track_zhixian2();        // ❌ UART通信方式（注释）
//  track_PID1(40,0.05);     // ❌ UART通信PID控制（注释）
//  track_PID2(40,0.05);     // ❌ UART通信PID控制（注释）
//  track_PID3(40,0.05);     // ❌ UART通信PID控制（注释）
}
```

## 2. GPIO数字量传输方式深度分析

### 2.1 STM32硬件配置
```c
// STM32 GPIO配置 - sensor.c
void SENSOR_GPIO_Config(void) {
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能GPIOE时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOE, ENABLE);
    
    // 配置PE8~PE15为8路传感器输入
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8  | GPIO_Pin_9  | GPIO_Pin_10 | GPIO_Pin_11 |
                                  GPIO_Pin_12 | GPIO_Pin_13 | GPIO_Pin_14 | GPIO_Pin_15;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;    // 上拉输入模式
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOE, &GPIO_InitStructure);
}
```

### 2.2 信号读取机制
```c
// STM32信号读取函数 - sensor.c
unsigned char digtal(unsigned char channel) {  // 读取1~8通道
    u8 value = 0;
    switch(channel) {
        case 1: if(PEin(8) == 1) value = 1; else value = 0; break;   // PE8
        case 2: if(PEin(9) == 1) value = 1; else value = 0; break;   // PE9
        case 3: if(PEin(10) == 1) value = 1; else value = 0; break;  // PE10
        case 4: if(PEin(11) == 1) value = 1; else value = 0; break;  // PE11
        case 5: if(PEin(12) == 1) value = 1; else value = 0; break;  // PE12
        case 6: if(PEin(13) == 1) value = 1; else value = 0; break;  // PE13
        case 7: if(PEin(14) == 1) value = 1; else value = 0; break;  // PE14
        case 8: if(PEin(15) == 1) value = 1; else value = 0; break;  // PE15
    }
    return value; 
}

// 宏定义简化访问 - sensor.h
#define D1 digtal(1)  // PE8
#define D2 digtal(2)  // PE9
#define D3 digtal(3)  // PE10
#define D4 digtal(4)  // PE11
#define D5 digtal(5)  // PE12
#define D6 digtal(6)  // PE13
#define D7 digtal(7)  // PE14
#define D8 digtal(8)  // PE15
```

### 2.3 信号含义
- **高电平 (1)**：传感器检测到**白色/浅色**表面（光线反射强）
- **低电平 (0)**：传感器检测到**黑色/深色**表面（光线被吸收）

### 2.4 循迹算法逻辑
```c
// STM32循迹算法 - line.c中的track_zhixian1()
void track_zhixian1() {    
    // 路口检测逻辑
    unsigned char num = 0, i;
    for(i=0; i<2; i++) {
        if(D1 == 1) num++; if(D2 == 1) num++; if(D3 == 1) num++; if(D4 == 1) num++;
        if(D5 == 1) num++; if(D6 == 1) num++; if(D7 == 1) num++; if(D8 == 1) num++;
        
        if(num >= (ADC_N-4)) {  // 检测到路口（4个以上传感器激活）
            lukou_num++; 
            if(lukou_num == 1) Delay_ms(10);
        }  
        num = 0;
    }
    
    if(lukou_num >= 2) { 
        lukou_num = 0; 
        motor(0,0);           // 停止
        Delay_ms(2000);       // 延时2秒
    }
    
    // 循迹控制逻辑（8位状态机）
    //                    D8 D7 D6 D5 D4 D3 D2 D1
    if     ((D1==1)&&(D2==0)&&(D3==0)&&(D4==0)&&(D5==0)&&(D6==0)&&(D7==0)&&(D8==0)) motor(0,50);   // 0000 0001 - 最左侧
    else if((D1==1)&&(D2==1)&&(D3==0)&&(D4==0)&&(D5==0)&&(D6==0)&&(D7==0)&&(D8==0)) motor(10,50);  // 0000 0011 - 左侧
    else if((D1==0)&&(D2==1)&&(D3==0)&&(D4==0)&&(D5==0)&&(D6==0)&&(D7==0)&&(D8==0)) motor(15,40);  // 0000 0010 - 左偏
    else if((D1==0)&&(D2==1)&&(D3==1)&&(D4==0)&&(D5==0)&&(D6==0)&&(D7==0)&&(D8==0)) motor(20,40);  // 0000 0110 - 左偏
    else if((D1==0)&&(D2==0)&&(D3==1)&&(D4==0)&&(D5==0)&&(D6==0)&&(D7==0)&&(D8==0)) motor(25,40);  // 0000 0100 - 左偏
    else if((D1==0)&&(D2==0)&&(D3==1)&&(D4==1)&&(D5==0)&&(D6==0)&&(D7==0)&&(D8==0)) motor(35,40);  // 0000 1100 - 左偏
    else if((D1==0)&&(D2==0)&&(D3==0)&&(D4==1)&&(D5==0)&&(D6==0)&&(D7==0)&&(D8==0)) motor(35,40);  // 0000 1000 - 左偏
    else if((D1==0)&&(D2==0)&&(D3==0)&&(D4==1)&&(D5==1)&&(D6==0)&&(D7==0)&&(D8==0)) motor(40,40);  // 0001 1000 - 中心位置
    else if((D1==0)&&(D2==0)&&(D3==0)&&(D4==0)&&(D5==1)&&(D6==0)&&(D7==0)&&(D8==0)) motor(40,35);  // 0001 0000 - 右偏
    else if((D1==0)&&(D2==0)&&(D3==0)&&(D4==0)&&(D5==1)&&(D6==1)&&(D7==0)&&(D8==0)) motor(40,35);  // 0011 0000 - 右偏
    else if((D1==0)&&(D2==0)&&(D3==0)&&(D4==0)&&(D5==0)&&(D6==1)&&(D7==0)&&(D8==0)) motor(40,25);  // 0010 0000 - 右偏
    else if((D1==0)&&(D2==0)&&(D3==0)&&(D4==0)&&(D5==0)&&(D6==1)&&(D7==1)&&(D8==0)) motor(40,20);  // 0110 0000 - 右偏
    else if((D1==0)&&(D2==0)&&(D3==0)&&(D4==0)&&(D5==0)&&(D6==0)&&(D7==1)&&(D8==0)) motor(40,15);  // 0100 0000 - 右偏
    else if((D1==0)&&(D2==0)&&(D3==0)&&(D4==0)&&(D5==0)&&(D6==0)&&(D7==1)&&(D8==1)) motor(50,10);  // 1100 0000 - 右侧
    else if((D1==0)&&(D2==0)&&(D3==0)&&(D4==0)&&(D5==0)&&(D6==0)&&(D7==0)&&(D8==1)) motor(50,0);   // 1000 0000 - 最右侧
    else motor(20,20);  // 默认前进
}
```

## 3. MSPM0G3507 GPIO数字量移植详解

### 3.1 SysConfig配置

#### 时钟配置
```c
// SysConfig时钟配置
SYSCFG_DL_SYSCTL_CLK_init();

// 手动配置示例（80MHz）
DL_SYSCTL_setSYSOSCFreq(DL_SYSCTL_SYSOSC_FREQ_BASE);
DL_SYSCTL_enableMFCLK();
```

#### GPIO配置
在SysConfig中配置：
- **端口选择**：GPIOA (PA0~PA7) 或 GPIOB (PB0~PB7)
- **方向**：Input
- **上拉电阻**：Enable Pull-up
- **中断**：Disable（轮询方式）
- **迟滞**：Disable

```c
// SysConfig生成的GPIO配置代码示例
static const DL_GPIO_initDigitalInputPinConfig gGPIOA_inputPinConfig[] = {
    {.pin = DL_GPIO_PIN_0, .mode = DL_GPIO_INPUT, .resistor = DL_GPIO_RESISTOR_PULL_UP},
    {.pin = DL_GPIO_PIN_1, .mode = DL_GPIO_INPUT, .resistor = DL_GPIO_RESISTOR_PULL_UP},
    {.pin = DL_GPIO_PIN_2, .mode = DL_GPIO_INPUT, .resistor = DL_GPIO_RESISTOR_PULL_UP},
    {.pin = DL_GPIO_PIN_3, .mode = DL_GPIO_INPUT, .resistor = DL_GPIO_RESISTOR_PULL_UP},
    {.pin = DL_GPIO_PIN_4, .mode = DL_GPIO_INPUT, .resistor = DL_GPIO_RESISTOR_PULL_UP},
    {.pin = DL_GPIO_PIN_5, .mode = DL_GPIO_INPUT, .resistor = DL_GPIO_RESISTOR_PULL_UP},
    {.pin = DL_GPIO_PIN_6, .mode = DL_GPIO_INPUT, .resistor = DL_GPIO_RESISTOR_PULL_UP},
    {.pin = DL_GPIO_PIN_7, .mode = DL_GPIO_INPUT, .resistor = DL_GPIO_RESISTOR_PULL_UP},
};
```

### 3.2 底层驱动实现

#### 传感器GPIO初始化
```c
// sensor.h - 端口定义
#define SENSOR_GPIO_PORT    GPIOA
#define SENSOR_PIN_MASK     (DL_GPIO_PIN_0 | DL_GPIO_PIN_1 | DL_GPIO_PIN_2 | DL_GPIO_PIN_3 | \
                            DL_GPIO_PIN_4 | DL_GPIO_PIN_5 | DL_GPIO_PIN_6 | DL_GPIO_PIN_7)

// sensor.c - 初始化函数
void SENSOR_GPIO_Config(void) {
    // SysConfig生成的初始化函数
    SYSCFG_DL_GPIO_init();
}
```

#### 单路传感器读取函数
```c
// sensor.c - 单路传感器读取（完全对应STM32版本）
uint8_t digtal(uint8_t channel) {
    uint8_t value = 0;
    uint32_t pin_mask;
    
    if (channel >= 1 && channel <= 8) {
        pin_mask = 1 << (channel - 1);  // 计算对应的引脚掩码
        
        // 读取GPIO状态
        if (DL_GPIO_readPins(SENSOR_GPIO_PORT, pin_mask) != 0) {
            value = 1;  // 高电平 - 白色表面
        } else {
            value = 0;  // 低电平 - 黑线
        }
    }
    
    return value;
}
```

#### 宏定义简化访问
```c
// sensor.h - 宏定义（与STM32完全一致）
#define D1 digtal(1)  // PA0
#define D2 digtal(2)  // PA1
#define D3 digtal(3)  // PA2
#define D4 digtal(4)  // PA3
#define D5 digtal(5)  // PA4
#define D6 digtal(6)  // PA5
#define D7 digtal(7)  // PA6
#define D8 digtal(8)  // PA7
```

#### 优化版本：批量读取
```c
// sensor.c - 优化版本：一次读取所有传感器
uint8_t read_all_sensors(void) {
    uint32_t gpio_state = DL_GPIO_readPins(SENSOR_GPIO_PORT, SENSOR_PIN_MASK);
    return (uint8_t)(gpio_state & 0xFF);  // 返回低8位
}

// 位操作版本的宏定义
#define D1_BIT (read_all_sensors() & 0x01)  // bit 0
#define D2_BIT (read_all_sensors() & 0x02)  // bit 1
#define D3_BIT (read_all_sensors() & 0x04)  // bit 2
#define D4_BIT (read_all_sensors() & 0x08)  // bit 3
#define D5_BIT (read_all_sensors() & 0x10)  // bit 4
#define D6_BIT (read_all_sensors() & 0x20)  // bit 5
#define D7_BIT (read_all_sensors() & 0x40)  // bit 6
#define D8_BIT (read_all_sensors() & 0x80)  // bit 7
```

### 3.3 循迹算法移植

#### 方案一：完全兼容移植（推荐）
```c
// line.c - 完全对应STM32的循迹算法
void track_zhixian1(void) {    
    unsigned char num = 0, i;
    static unsigned char lukou_num = 0;  // 路口计数器
    
    // 路口检测逻辑（与STM32完全一致）
    for(i = 0; i < 2; i++) {
        if(D1 == 1) num++; if(D2 == 1) num++; if(D3 == 1) num++; if(D4 == 1) num++;
        if(D5 == 1) num++; if(D6 == 1) num++; if(D7 == 1) num++; if(D8 == 1) num++;
        
        if(num >= (ADC_N - 4)) {  // ADC_N = 8，检测到4个以上传感器激活
            lukou_num++; 
            if(lukou_num == 1) delay_ms(10);  // 第一次检测到时延时10ms
        }  
        num = 0;
    }
    
    // 路口处理
    if(lukou_num >= 2) { 
        lukou_num = 0; 
        motor_control(0, 0);    // 停止电机
        delay_ms(2000);         // 延时2秒
        return;
    }
    
    // 循迹控制逻辑（与STM32完全一致）
    //                    D8 D7 D6 D5 D4 D3 D2 D1 (从右到左：1号到8号传感器)
    if     ((D1==1)&&(D2==0)&&(D3==0)&&(D4==0)&&(D5==0)&&(D6==0)&&(D7==0)&&(D8==0)) motor_control(0,50);   // 0000 0001
    else if((D1==1)&&(D2==1)&&(D3==0)&&(D4==0)&&(D5==0)&&(D6==0)&&(D7==0)&&(D8==0)) motor_control(10,50);  // 0000 0011
    else if((D1==0)&&(D2==1)&&(D3==0)&&(D4==0)&&(D5==0)&&(D6==0)&&(D7==0)&&(D8==0)) motor_control(15,40);  // 0000 0010
    else if((D1==0)&&(D2==1)&&(D3==1)&&(D4==0)&&(D5==0)&&(D6==0)&&(D7==0)&&(D8==0)) motor_control(20,40);  // 0000 0110
    else if((D1==0)&&(D2==0)&&(D3==1)&&(D4==0)&&(D5==0)&&(D6==0)&&(D7==0)&&(D8==0)) motor_control(25,40);  // 0000 0100
    else if((D1==0)&&(D2==0)&&(D3==1)&&(D4==1)&&(D5==0)&&(D6==0)&&(D7==0)&&(D8==0)) motor_control(35,40);  // 0000 1100
    else if((D1==0)&&(D2==0)&&(D3==0)&&(D4==1)&&(D5==0)&&(D6==0)&&(D7==0)&&(D8==0)) motor_control(35,40);  // 0000 1000
    else if((D1==0)&&(D2==0)&&(D3==0)&&(D4==1)&&(D5==1)&&(D6==0)&&(D7==0)&&(D8==0)) motor_control(40,40);  // 0001 1000 - 中心
    else if((D1==0)&&(D2==0)&&(D3==0)&&(D4==0)&&(D5==1)&&(D6==0)&&(D7==0)&&(D8==0)) motor_control(40,35);  // 0001 0000
    else if((D1==0)&&(D2==0)&&(D3==0)&&(D4==0)&&(D5==1)&&(D6==1)&&(D7==0)&&(D8==0)) motor_control(40,35);  // 0011 0000
    else if((D1==0)&&(D2==0)&&(D3==0)&&(D4==0)&&(D5==0)&&(D6==1)&&(D7==0)&&(D8==0)) motor_control(40,25);  // 0010 0000
    else if((D1==0)&&(D2==0)&&(D3==0)&&(D4==0)&&(D5==0)&&(D6==1)&&(D7==1)&&(D8==0)) motor_control(40,20);  // 0110 0000
    else if((D1==0)&&(D2==0)&&(D3==0)&&(D4==0)&&(D5==0)&&(D6==0)&&(D7==1)&&(D8==0)) motor_control(40,15);  // 0100 0000
    else if((D1==0)&&(D2==0)&&(D3==0)&&(D4==0)&&(D5==0)&&(D6==0)&&(D7==1)&&(D8==1)) motor_control(50,10);  // 1100 0000
    else if((D1==0)&&(D2==0)&&(D3==0)&&(D4==0)&&(D5==0)&&(D6==0)&&(D7==0)&&(D8==1)) motor_control(50,0);   // 1000 0000
    else motor_control(20,20);  // 默认前进
}
```

#### 方案二：优化版本（性能更好）
```c
// line.c - 优化版本：使用位操作
void track_zhixian1_optimized(void) {    
    static unsigned char lukou_num = 0;
    uint8_t sensor_state = read_all_sensors();  // 一次读取所有传感器
    
    // 路口检测：计算激活的传感器数量
    uint8_t active_count = 0;
    uint8_t temp = sensor_state;
    while (temp) {
        active_count += temp & 1;
        temp >>= 1;
    }
    
    // 路口处理
    if (active_count >= 4) {
        lukou_num++;
        if (lukou_num >= 2) {
            lukou_num = 0;
            motor_control(0, 0);
            delay_ms(2000);
            return;
        }
    }
    
    // 循迹控制：使用查找表方式
    switch (sensor_state) {
        case 0x01: motor_control(0, 50); break;    // 0000 0001
        case 0x03: motor_control(10, 50); break;   // 0000 0011
        case 0x02: motor_control(15, 40); break;   // 0000 0010
        case 0x06: motor_control(20, 40); break;   // 0000 0110
        case 0x04: motor_control(25, 40); break;   // 0000 0100
        case 0x0C: motor_control(35, 40); break;   // 0000 1100
        case 0x08: motor_control(35, 40); break;   // 0000 1000
        case 0x18: motor_control(40, 40); break;   // 0001 1000 - 中心
        case 0x10: motor_control(40, 35); break;   // 0001 0000
        case 0x30: motor_control(40, 35); break;   // 0011 0000
        case 0x20: motor_control(40, 25); break;   // 0010 0000
        case 0x60: motor_control(40, 20); break;   // 0110 0000
        case 0x40: motor_control(40, 15); break;   // 0100 0000
        case 0xC0: motor_control(50, 10); break;   // 1100 0000
        case 0x80: motor_control(50, 0); break;    // 1000 0000
        default: motor_control(20, 20); break;     // 默认前进
    }
}
```

### 3.4 电机控制接口
```c
// motor.h - 电机控制接口定义
void motor_control(int left_speed, int right_speed);

// motor.c - 电机控制实现（使用PWM）
void motor_control(int left_speed, int right_speed) {
    // 左电机控制
    if (left_speed >= 0) {
        DL_TimerA_setCaptureCompareValue(MOTOR_LEFT_TIMER, left_speed * 10, DL_TIMER_CC_0_INDEX);
        DL_GPIO_clearPins(MOTOR_LEFT_DIR_PORT, MOTOR_LEFT_DIR_PIN);  // 正转
    } else {
        DL_TimerA_setCaptureCompareValue(MOTOR_LEFT_TIMER, (-left_speed) * 10, DL_TIMER_CC_0_INDEX);
        DL_GPIO_setPins(MOTOR_LEFT_DIR_PORT, MOTOR_LEFT_DIR_PIN);    // 反转
    }
    
    // 右电机控制
    if (right_speed >= 0) {
        DL_TimerA_setCaptureCompareValue(MOTOR_RIGHT_TIMER, right_speed * 10, DL_TIMER_CC_0_INDEX);
        DL_GPIO_clearPins(MOTOR_RIGHT_DIR_PORT, MOTOR_RIGHT_DIR_PIN);  // 正转
    } else {
        DL_TimerA_setCaptureCompareValue(MOTOR_RIGHT_TIMER, (-right_speed) * 10, DL_TIMER_CC_0_INDEX);
        DL_GPIO_setPins(MOTOR_RIGHT_DIR_PORT, MOTOR_RIGHT_DIR_PIN);    // 反转
    }
}
```

### 3.5 延时函数
```c
// delay.h
void delay_ms(uint32_t ms);

// delay.c - 使用SysTick实现
void delay_ms(uint32_t ms) {
    DL_Common_delayCycles(ms * (CPUCLK_FREQ / 1000));  // 80MHz时钟
}
```

## 4. 完整主程序框架

```c
// main.c - MSPM0G3507主程序
#include "ti_msp_dl_config.h"
#include "sensor.h"
#include "motor.h"
#include "line.h"
#include "delay.h"

int main(void) {
    // 系统初始化
    SYSCFG_DL_init();
    
    // 模块初始化
    SENSOR_GPIO_Config();    // 传感器GPIO初始化
    motor_init();            // 电机初始化
    
    // 电机停止，等待系统稳定
    motor_control(0, 0);
    delay_ms(1000);
    
    // 主循环
    while (1) {
        track_zhixian1();    // GPIO数字量循迹
        delay_ms(10);        // 控制周期10ms
    }
}
```

## 5. 移植优势与注意事项

### 5.1 MSPM0G3507优势
1. **更高性能**：80MHz vs 72MHz，响应更快
2. **更低功耗**：M0+内核，适合电池供电
3. **SysConfig**：图形化配置，降低开发难度
4. **丰富GPIO**：84个GPIO，引脚资源充足

### 5.2 移植注意事项
1. **引脚映射**：STM32的PE8~PE15 → MSPM0G3507的PA0~PA7
2. **时钟配置**：确保GPIO时钟正确使能
3. **上拉电阻**：必须使能内部上拉电阻
4. **电平逻辑**：确认传感器输出逻辑与MCU输入逻辑一致

### 5.3 性能优化建议
1. **批量读取**：使用`read_all_sensors()`一次读取所有传感器
2. **查找表**：使用switch-case替代大量if-else判断
3. **中断驱动**：可考虑使用GPIO中断检测传感器变化
4. **DMA优化**：如果需要高频采样，可考虑DMA方式

## 6. 总结

GPIO数字量传输方式是三种通信方式中**最简单、最直接、最可靠**的方式。移植到MSPM0G3507主要工作是：

1. **硬件映射**：STM32的PE8~PE15 → MSPM0G3507的PA0~PA7
2. **驱动适配**：使用MSPM0G3507的GPIO API替换STM32的GPIO操作
3. **算法保持**：循迹算法逻辑完全不变，确保行为一致性
4. **性能优化**：利用MSPM0G3507的高性能特性进行优化

这种移植方式**兼容性最好**，**调试最简单**，**可靠性最高**，是推荐的移植方案。